<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_picking_inherit_form_view" model="ir.ui.view">
        <field name="name">stock.picking.inherit.form.view</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='button_validate'][1]" position="attributes">
                <attribute name="invisible">(state in ('waiting','confirmed')) or (show_validate == False) or (picking_type_code == 'outgoing') or (is_internal_return_approval == True)</attribute>
            </xpath>

            <xpath expr="//button[@name='button_validate'][2]" position="replace">
                <button name="button_validate" invisible="state not in ('waiting', 'confirmed') or show_validate == False or picking_type_code == 'outgoing' or is_internal_return_approval == True"
                        string="Validate" type="object" groups="stock.group_stock_user" class="o_btn_validate" data-hotkey="v"/>

                <button name="button_validate"
                        invisible="state not in ('assigned') or show_validate == False or approve_status == 'pending' or picking_type_code != 'outgoing' or is_internal_return_approval == True"
                        string="Validate" type="object" groups="stock.group_stock_user" class="o_btn_validate" data-hotkey="v"/>

                <button name="button_validate"
                        invisible="state not in ('assigned') or is_internal_return_approval == False or show_internal_return_validate == False"
                        string="Validate" type="object" groups="stock.group_stock_user" class="o_btn_validate" data-hotkey="v"/>

                <field name="is_internal_return_approval" invisible="1"/>
                <field name="show_internal_return_validate" invisible="1"/>
                <field name="internal_approval_count" invisible="1"/>
            </xpath>

            <xpath expr="//button[@name='button_validate']" position="after">
                <button name="create_approval" string="Submit Approval" type="object"
                    invisible="state not in ('assigned') or approval_count > 0 or picking_type_code != 'outgoing'"/>

                <button name="send_for_approval" string="Send IT/RTN Approval" type="object"
                    invisible="state not in ('assigned') or internal_approval_count > 0 or is_internal_return_approval == False"/>
            </xpath>

            <xpath expr="//div[@name='button_box']/button[@name='action_see_packages']" position="before">
                <button type="object" name="view_transfer_approval" class="oe_stat_button" invisible="approval_count == 0" icon="fa-list">
                    <field name="approval_count" widget="statinfo" nolabel="1"/>
                    <field name="approve_status" widget="statinfo" nolabel="1"/>
                </button>

                <button type="object" name="view_internal_return_approval" class="oe_stat_button" invisible="internal_approval_count == 0" icon="fa-list">
                    <field name="internal_approval_count" widget="statinfo" nolabel="1"/>
                    <field name="approve_status" widget="statinfo" nolabel="1"/>
                </button>
            </xpath>

            <xpath expr="//form[1]/sheet[1]/group[1]/group[1]/field[@name='location_id']" position="after">
                <field name="contact" invisible="picking_type_code != 'outgoing'"/>
                <field name="select_con_del_partner" invisible="picking_type_code != 'outgoing' or contact == False"/>
                <field name="contact_name" readonly="1" force_save="1"
                    invisible="picking_type_code != 'outgoing'"/>
                <field name="contact_address" readonly="1" force_save="1"
                    invisible="picking_type_code != 'outgoing'"/>
            </xpath>

            <field name="origin" position="after">
                <field name="customer_name"/>
                <field name="customer_reference"/>
                <field name="delivery_location"/>
            </field>

        </field>
    </record>
</odoo>
