<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Sequences for sale.order -->
        <record id="seq_po_service_item" model="ir.sequence">
            <field name="name">Seq PO Service Item</field>
            <field name="code">seq.po.service.item</field>
            <field name="prefix">SVCPO</field>
            <field name="padding">5</field>
            <field name="company_id" eval="False"/>
        </record>

    </data>
</odoo>