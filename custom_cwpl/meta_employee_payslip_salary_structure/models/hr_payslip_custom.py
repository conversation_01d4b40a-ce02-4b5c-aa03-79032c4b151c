# -*- coding:utf-8 -*-

from datetime import datetime

from odoo import api, fields, models, _


class HrPayslipCustom(models.Model):
    _inherit='hr.payslip'
    
    employee_id = fields.Many2one(
        'hr.employee', string='Employee', required=True, readonly=True,
        states={'draft': [('readonly', False)], 'verify': [('readonly', False)]},
        domain="['|', ('company_id', '=', False), ('company_id', '=', company_id), '|', ('active', '=', True), ('active', '=', False)]")

    date_from_joining=fields.Date(string="Employee Joining Date", compute='_get_date_from_joining')

    @api.depends("employee_id")
    def _get_date_from_joining(self):
        for x_slip in self:
            dt=x_slip.payslip_run_id.date_start
            x_dt=x_slip.employee_id.x_studio_joining_date

            dt_str=str(dt)
            x_dt_str=str(x_dt)
            
            dt_lst=dt_str.split('-',2)
            x_dt_lst=x_dt_str.split('-',2)
            
            dt_day=int(dt_lst[2])
            x_dt_day=int(x_dt_lst[2])
            
            dt_month=int(dt_lst[1])
            x_dt_month=int(x_dt_lst[1])
            
            dt_year=int(dt_lst[0])
            x_dt_year=int(x_dt_lst[0])
                      
            if int(datetime.strptime(str(x_dt),'%Y-%m-%d').year)==dt_year and int(datetime.strptime(str(x_dt),'%Y-%m-%d').month)==dt_month:
                if int(datetime.strptime(str(x_dt),'%Y-%m-%d').day)>1:                    
                    x_slip.date_from=x_slip.employee_id.x_studio_joining_date
                    x_slip.date_from_joining=x_slip.employee_id.x_studio_joining_date
                else:
                    x_slip.date_from_joining=x_slip.employee_id.x_studio_joining_date
            else:
                x_slip.date_from_joining=x_slip.employee_id.x_studio_joining_date
                
                